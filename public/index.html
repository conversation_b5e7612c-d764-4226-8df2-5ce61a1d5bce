<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
    <!-- React CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script crossorigin src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-auth-compat.js"></script>
    <style>
        .gallery {
            padding: 2rem;
            background: #fff;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .gallery-item {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .gallery-item:hover {
            transform: translateY(-5px);
        }

        .gallery-image {
            width: 100%;
            height: 300px;
            object-fit: cover;
            display: block;
        }

        @media (max-width: 768px) {
            .gallery-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }
        }

        @media (max-width: 480px) {
            .gallery-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Barber Brothers</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#gallery">Gallery</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <header class="hero">
        <div class="container text-center">
            <h1>Barber Brothers Legacy</h1>
            <p>Premium cuts, classic styles, modern expertise</p>
            <a href="#appointment" class="btn btn-primary btn-lg">Book Now</a>
        </div>
    </header>

    <!-- Gallery Section -->
    <section id="gallery" class="gallery">
        <div class="container text-center mb-4">
            <h2>Our Work</h2>
            <p>Check out some of our finest cuts and styles</p>
        </div>
        <div class="gallery-grid">
            <div class="gallery-item">
                <img src="../images/a1.JPG" alt="Barber Brothers Haircut 1" class="gallery-image">
            </div>
            <div class="gallery-item">
                <img src="../images/a2.JPG" alt="Barber Brothers Haircut 2" class="gallery-image">
            </div>
            <div class="gallery-item">
                <img src="../images/a3.JPG" alt="Barber Brothers Haircut 3" class="gallery-image">
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2>About Andre</h2>
                    <p>With over 15 years of experience, Andre has mastered the art of barbering. His passion for creating perfect cuts and styles has earned him a loyal clientele.</p>
                    <p>At Barber Brothers Legacy, we believe in providing not just a haircut, but an experience that leaves you looking and feeling your best.</p>
                </div>
                <div class="col-md-6">
                    <img src="images/about.jpeg" alt="About Barber Brothers" class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services py-5 bg-light">
        <div class="container text-center">
            <h2 class="mb-4">Our Services</h2>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">Haircut</h5>
                            <p class="card-text">Precision cut tailored to your style and face shape.</p>
                            <p class="price">$30</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">Beard Trim</h5>
                            <p class="card-text">Expert beard shaping and maintenance.</p>
                            <p class="price">$20</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">Full Service</h5>
                            <p class="card-text">Haircut, beard trim, and hot towel treatment.</p>
                            <p class="price">$45</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Appointment Section -->
    <section id="appointment" class="appointment py-5">
        <div class="container">
            <h2 class="text-center mb-4">Book an Appointment</h2>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <form id="appointment-form">
                        <div class="mb-3">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" required>
                        </div>
                        <div class="mb-3">
                            <label for="service" class="form-label">Service</label>
                            <select class="form-select" id="service" required>
                                <option value="">Select a service</option>
                                <option value="Haircut">Haircut - $30</option>
                                <option value="Beard Trim">Beard Trim - $20</option>
                                <option value="Full Service">Full Service - $45</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="date" class="form-label">Date</label>
                                <input type="date" class="form-control" id="date" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="time" class="form-label">Time</label>
                                <input type="time" class="form-control" id="time" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">Special Requests (Optional)</label>
                            <textarea class="form-control" id="notes" rows="3"></textarea>
                        </div>
                        <div id="form-message" class="alert" style="display: none;"></div>
                        <button type="submit" class="btn btn-primary w-100">Book Now</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Community Feed Container -->
    <div id="community-feed-root"></div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p>© 2023 Barber Brothers Legacy. All rights reserved.</p>
            <p>Contact: (404) 309-1971 | <EMAIL></p>
            <div class="social-icons">
                <a href="#" class="text-white me-3"><i class="bi bi-facebook"></i></a>
                <a href="#" class="text-white me-3"><i class="bi bi-instagram"></i></a>
                <a href="#" class="text-white"><i class="bi bi-twitter"></i></a>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="js/main.js"></script>
    <script src="js/appointment.js"></script>
    
    <!-- Community Feed Script -->
    <script src="js/community-feed.js" type="text/babel"></script>
</body>
</html>
