document.addEventListener('DOMContentLoaded', function() {
    console.log('Google Sign-in service loaded');

    // Initialize Firebase
    initializeFirebase();

    // Initialize Google Sign-in buttons
    initGoogleSignInButtons();
});

// Initialize Firebase with your config
function initializeFirebase() {
    // Firebase configuration
    const firebaseConfig = {
        apiKey: "AIzaSyAHqpO8PPXmZIfype7dsViz3chKcmLdmpY",
        authDomain: "barber-brothers-legacy.firebaseapp.com",
        projectId: "barber-brothers-legacy",
        storageBucket: "barber-brothers-legacy.appspot.com",
        messagingSenderId: "946338896038",
        appId: "1:946338896038:web:d65f5bef7973127d1abc67"
    };

    // Initialize Firebase
    if (typeof firebase !== 'undefined' && !firebase.apps.length) {
        firebase.initializeApp(firebaseConfig);
        console.log('Firebase initialized successfully');
    } else if (typeof firebase === 'undefined') {
        console.error('Firebase is not loaded');
    }
}

// Initialize Google Sign-in buttons for service cards and navbar
function initGoogleSignInButtons() {
    const serviceCards = document.querySelectorAll('.google-signin-btn');
    const navbarSignInBtn = document.getElementById('navbar-signin-btn');
    const signOutLink = document.getElementById('sign-out-link');

    // Service card sign-in buttons
    serviceCards.forEach(card => {
        card.addEventListener('click', function() {
            const serviceType = this.dataset.service;
            console.log(`Service card clicked: ${serviceType}`);

            // Trigger Google Sign-in
            signInWithGoogle(serviceType);
        });
    });

    // Navbar sign-in button
    if (navbarSignInBtn) {
        navbarSignInBtn.addEventListener('click', function() {
            console.log('Navbar sign-in clicked');
            signInWithGoogle('navbar');
        });
    }

    // Sign-out link
    if (signOutLink) {
        signOutLink.addEventListener('click', function(e) {
            e.preventDefault();
            signOut();
        });
    }

    console.log(`Initialized ${serviceCards.length} service card sign-in buttons and navbar controls`);
}

// Google Sign-in function
function signInWithGoogle(serviceType) {
    if (typeof firebase === 'undefined') {
        console.error('Firebase is not loaded');
        showMessage('Authentication service is not available. Please try again later.', 'error');
        return;
    }

    const auth = firebase.auth();
    const provider = new firebase.auth.GoogleAuthProvider();

    // Add additional scopes if needed
    provider.addScope('email');
    provider.addScope('profile');

    // Set custom parameters
    provider.setCustomParameters({
        prompt: 'select_account'
    });

    console.log('Starting Google sign-in process...');

    auth.signInWithPopup(provider)
        .then((result) => {
            // Successful sign-in
            const user = result.user;
            console.log('User signed in successfully:', user.displayName);

            // Show success message
            showMessage(`Welcome ${user.displayName}! You can now book your ${getServiceName(serviceType)} appointment.`, 'success');

            // Store selected service in localStorage for later use
            localStorage.setItem('selectedService', serviceType);

            // Scroll to contact section after sign-in
            setTimeout(() => {
                const contactSection = document.getElementById('contact');
                if (contactSection) {
                    contactSection.scrollIntoView({ behavior: 'smooth' });
                }
            }, 2000);

        })
        .catch((error) => {
            console.error('Sign-in error:', error);

            let errorMessage = 'Sign-in failed. Please try again.';

            switch (error.code) {
                case 'auth/popup-closed-by-user':
                    errorMessage = 'Sign-in was cancelled. Please try again.';
                    break;
                case 'auth/popup-blocked':
                    errorMessage = 'Pop-up was blocked. Please allow pop-ups and try again.';
                    break;
                case 'auth/network-request-failed':
                    errorMessage = 'Network error. Please check your connection and try again.';
                    break;
                default:
                    errorMessage = `Sign-in failed: ${error.message}`;
            }

            showMessage(errorMessage, 'error');
        });
}

// Sign out function
function signOut() {
    if (typeof firebase !== 'undefined' && firebase.auth) {
        firebase.auth().signOut()
            .then(() => {
                console.log('User signed out successfully');
                showMessage('You have been signed out successfully.', 'success');
            })
            .catch((error) => {
                console.error('Sign out error:', error);
                showMessage('Error signing out. Please try again.', 'error');
            });
    }
}

// Helper function to get service name
function getServiceName(serviceType) {
    switch (serviceType) {
        case 'adult':
            return 'Adult Haircut';
        case 'kids':
            return 'Kids Haircut';
        case 'full':
            return 'Full Service';
        case 'navbar':
            return 'service';
        default:
            return 'service';
    }
}

// Function to show messages (success/error)
function showMessage(message, type = 'success') {
    // Remove any existing messages
    const existingMessages = document.querySelectorAll('.auth-alert');
    existingMessages.forEach(msg => msg.remove());

    // Create new message
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} auth-alert position-fixed`;
    messageDiv.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;

    messageDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            <div>
                <strong>${type === 'success' ? 'Success!' : 'Error'}</strong>
                <div>${message}</div>
            </div>
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    // Add to body
    document.body.appendChild(messageDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 5000);
}

// Monitor authentication state
function monitorAuthState() {
    if (typeof firebase !== 'undefined') {
        const auth = firebase.auth();

        auth.onAuthStateChanged((user) => {
            if (user) {
                console.log('User is signed in:', user.displayName);
                updateUIForSignedInUser(user);
            } else {
                console.log('User is signed out');
                updateUIForSignedOutUser();
            }
        });
    }
}

// Update UI for signed in user
function updateUIForSignedInUser(user) {
    // Update navbar to show user menu
    const userMenu = document.getElementById('user-menu');
    const signinMenu = document.getElementById('signin-menu');
    const userAvatar = document.getElementById('user-avatar');
    const userName = document.getElementById('user-name');

    if (userMenu && signinMenu) {
        userMenu.classList.remove('d-none');
        signinMenu.classList.add('d-none');

        if (userAvatar && user.photoURL) {
            userAvatar.src = user.photoURL;
        }
        if (userName) {
            userName.textContent = user.displayName || 'User';
        }
    }

    // Update service card overlays to show "Book Now" instead of "Sign in to book"
    const serviceOverlays = document.querySelectorAll('.service-overlay span');
    serviceOverlays.forEach(overlay => {
        overlay.textContent = 'Click to book';
    });

    // Update service cards to go directly to booking instead of sign-in
    const serviceCards = document.querySelectorAll('.google-signin-btn');
    serviceCards.forEach(card => {
        card.classList.remove('google-signin-btn');
        card.classList.add('booking-btn');

        // Remove old event listeners by cloning the element
        const newCard = card.cloneNode(true);
        card.parentNode.replaceChild(newCard, card);

        // Add new event listener for booking
        newCard.addEventListener('click', function() {
            const serviceType = this.dataset.service;
            const serviceName = getServiceName(serviceType);

            // Pre-select service in contact form
            const serviceSelect = document.getElementById('service');
            if (serviceSelect) {
                const option = Array.from(serviceSelect.options).find(opt =>
                    opt.textContent.toLowerCase().includes(serviceName.toLowerCase())
                );
                if (option) {
                    option.selected = true;
                }
            }

            // Scroll to contact section
            const contactSection = document.getElementById('contact');
            if (contactSection) {
                contactSection.scrollIntoView({ behavior: 'smooth' });
            }

            showMessage(`Ready to book your ${serviceName}! Please fill out the form below.`, 'success');
        });
    });
}

// Update UI for signed out user
function updateUIForSignedOutUser() {
    // Update navbar to show sign-in button
    const userMenu = document.getElementById('user-menu');
    const signinMenu = document.getElementById('signin-menu');

    if (userMenu && signinMenu) {
        userMenu.classList.add('d-none');
        signinMenu.classList.remove('d-none');
    }

    // Update service card overlays back to "Sign in to book"
    const serviceOverlays = document.querySelectorAll('.service-overlay span');
    serviceOverlays.forEach(overlay => {
        overlay.textContent = 'Sign in to book';
    });

    // Update service cards back to sign-in functionality
    const serviceCards = document.querySelectorAll('.booking-btn');
    serviceCards.forEach(card => {
        card.classList.remove('booking-btn');
        card.classList.add('google-signin-btn');

        // Remove old event listeners by cloning the element
        const newCard = card.cloneNode(true);
        card.parentNode.replaceChild(newCard, card);

        // Add sign-in event listener
        newCard.addEventListener('click', function() {
            const serviceType = this.dataset.service;
            signInWithGoogle(serviceType);
        });
    });
}

// Initialize auth state monitoring when Firebase is ready
setTimeout(() => {
    monitorAuthState();
}, 1000);